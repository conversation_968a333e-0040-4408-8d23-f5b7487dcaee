.input-lower-tray {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: flex-end;
  position: absolute;
  bottom: 12px;
  right: 24px;

  &__button-group {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  &__temperature-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    white-space: nowrap;
    flex-shrink: 0;
    // Remove min-width to allow natural sizing
    width: fit-content;
  }

  &__temperature-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
  }

  &__temperature-text {
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 500;
    color: white;
    line-height: 1;
    // Remove flex: 1 and text-align: center to prevent stretching
    overflow: visible;
    text-overflow: clip;
  }

  &__settings-button,
  &__send-button {
    // These will use the circleIconButton class from useThemeStyles
    font-family: 'Roboto', sans-serif;
  }

  &__settings-icon,
  &__send-icon {
    width: 20px;
    height: 20px;
  }

  // Responsive adjustments for smaller screens
  @media (max-width: 768px) {
    &__temperature-badge {
      padding: 6px 10px;
    }

    &__temperature-text {
      font-size: 13px;
    }
  }
}
